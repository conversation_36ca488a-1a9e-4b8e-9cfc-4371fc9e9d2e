import unittest
import uuid
from datetime import datetime
from unittest.mock import patch

import pytest

from holmatro_customer_portal.schemas.opensearch_schemas import ProductDocument
from holmatro_customer_portal.utils.open_search.opensearch_client import OpenSearchClient


class TestOpenSearchClient(unittest.TestCase):
    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.OpenSearch")
    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.Env")
    def setUp(self, mock_env, mock_opensearch):
        mock_env.OPENSEARCH_HOST.get.return_value = "localhost"
        mock_env.OPENSEARCH_PORT.get.return_value = "9200"
        mock_env.OPENSEARCH_USERNAME.get.return_value = "user"
        mock_env.OPENSEARCH_PASSWORD.get.return_value = "password"
        self.mock_client = mock_opensearch.return_value
        self.opensearch_client = OpenSearchClient(is_fuzzy_search_enabled=False, index_name="test_index")

    def test_create_index_not_exists(self):
        self.mock_client.indices.exists.return_value = False
        index_name = "test_index"
        mappings = {"properties": {"field": {"type": "keyword"}}}
        self.opensearch_client.create_index(mappings)
        self.mock_client.indices.exists.assert_called_once_with(index=index_name)
        self.mock_client.indices.create.assert_called_once_with(index=index_name, body={"mappings": mappings})

    def test_create_index_exists(self):
        self.mock_client.indices.exists.return_value = True
        index_name = "test_index"
        mappings = {"properties": {"field": {"type": "keyword"}}}
        self.opensearch_client.create_index(mappings)
        self.mock_client.indices.exists.assert_called_once_with(index=index_name)
        self.mock_client.indices.create.assert_not_called()

    def test_create_index_exception(self):
        self.mock_client.indices.exists.return_value = False
        self.mock_client.indices.create.side_effect = Exception("Test error")
        index_name = "test_index"
        mappings = {"properties": {"field": {"type": "keyword"}}}
        with self.assertRaises(Exception):
            self.opensearch_client.create_index(mappings)
        self.mock_client.indices.exists.assert_called_once_with(index=index_name)
        self.mock_client.indices.create.assert_called_once_with(index=index_name, body={"mappings": mappings})

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_bulk_index_success(self, mock_bulk):
        mock_bulk.return_value = (1, [])
        index_name = "test_index"
        documents = [
            ProductDocument(
                id=uuid.uuid4(),
                product_id=1,
                umid=uuid.uuid4(),
                article_number="A1",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            )
        ]
        success, failed = self.opensearch_client.bulk_index(documents)
        self.assertEqual(success, 1)
        self.assertEqual(failed, [])
        mock_bulk.assert_called_once()

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_bulk_index_failure(self, mock_bulk):
        mock_bulk.return_value = (0, [{"error": "some error"}])
        documents = [
            ProductDocument(
                id=uuid.uuid4(),
                product_id=1,
                umid=uuid.uuid4(),
                article_number="A1",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            )
        ]
        success, failed = self.opensearch_client.bulk_index(documents)
        self.assertEqual(success, 0)
        self.assertEqual(failed, [{"error": "some error"}])
        mock_bulk.assert_called_once()

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_bulk_index_exception(self, mock_bulk):
        mock_bulk.side_effect = Exception("Bulk error")
        documents = [
            ProductDocument(
                id=uuid.uuid4(),
                product_id=1,
                umid=uuid.uuid4(),
                article_number="A1",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            )
        ]
        with self.assertRaises(Exception):
            self.opensearch_client.bulk_index(documents)
        mock_bulk.assert_called_once()

    def test_delete_index_exists(self):
        """Test deleting an index that exists."""
        self.mock_client.indices.exists.return_value = True
        index_name = "test_index"

        self.opensearch_client.delete_index()

        self.mock_client.indices.exists.assert_called_once_with(index=index_name)
        self.mock_client.indices.delete.assert_called_once_with(index=index_name)

    def test_delete_index_not_exists(self):
        """Test deleting an index that doesn't exist."""
        self.mock_client.indices.exists.return_value = False
        index_name = "test_index"

        self.opensearch_client.delete_index()

        self.mock_client.indices.exists.assert_called_once_with(index=index_name)
        self.mock_client.indices.delete.assert_not_called()

    def test_delete_index_exception(self):
        """Test exception handling when deleting an index."""
        self.mock_client.indices.exists.return_value = True
        self.mock_client.indices.delete.side_effect = Exception("Delete error")
        index_name = "test_index"

        with self.assertRaises(Exception):
            self.opensearch_client.delete_index()

        self.mock_client.indices.exists.assert_called_once_with(index=index_name)
        self.mock_client.indices.delete.assert_called_once_with(index=index_name)

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_update_documents_success(self, mock_bulk):
        """Test updating documents successfully."""
        mock_bulk.return_value = (2, [])
        documents = [
            ProductDocument(
                id=uuid.uuid4(),
                product_id=1,
                umid=uuid.uuid4(),
                article_number="A1",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            ),
            ProductDocument(
                id=uuid.uuid4(),
                product_id=2,
                umid=uuid.uuid4(),
                article_number="A2",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            ),
        ]

        success, failed = self.opensearch_client.update_documents(documents)

        self.assertEqual(success, 2)
        self.assertEqual(failed, [])
        mock_bulk.assert_called_once()

        # Verify the actions passed to bulk have correct op_type
        call_args = mock_bulk.call_args[0]
        actions = call_args[1]  # bulk(client, actions)
        for action in actions:
            self.assertEqual(action["_op_type"], "update")

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_update_documents_exception(self, mock_bulk):
        """Test exception handling when updating documents."""
        mock_bulk.side_effect = Exception("Update error")
        documents = [
            ProductDocument(
                id=uuid.uuid4(),
                product_id=1,
                umid=uuid.uuid4(),
                article_number="A1",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            )
        ]

        with self.assertRaises(Exception):
            self.opensearch_client.update_documents(documents)

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_delete_documents_success(self, mock_bulk):
        """Test deleting documents successfully."""
        mock_bulk.return_value = (2, [])
        document_ids = ["id1", "id2"]

        success, failed = self.opensearch_client.delete_documents(document_ids)

        self.assertEqual(success, 2)
        self.assertEqual(failed, [])
        mock_bulk.assert_called_once()

        # Verify the actions passed to bulk have correct op_type
        call_args = mock_bulk.call_args[0]
        actions = call_args[1]  # bulk(client, actions)
        for action in actions:
            self.assertEqual(action["_op_type"], "delete")
            self.assertIn(action["_id"], document_ids)

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_delete_documents_with_404_ignored(self, mock_bulk):
        """Test deleting documents with 404 errors ignored."""
        mock_bulk.return_value = (1, [{"delete": {"_id": "missing_id", "status": 404}}])
        document_ids = ["id1", "missing_id"]

        success, failed = self.opensearch_client.delete_documents(document_ids)

        self.assertEqual(success, 1)
        mock_bulk.assert_called_once()

        # Verify ignore_status parameter is passed
        call_args = mock_bulk.call_args
        self.assertIn("ignore_status", call_args[1])
        self.assertEqual(call_args[1]["ignore_status"], [404])

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_delete_documents_exception(self, mock_bulk):
        """Test exception handling when deleting documents."""
        mock_bulk.side_effect = Exception("Delete error")
        document_ids = ["id1", "id2"]

        with self.assertRaises(Exception):
            self.opensearch_client.delete_documents(document_ids)

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_delta_update_upsert_only(self, mock_bulk):
        """Test delta update with only upsert documents."""
        mock_bulk.return_value = (1, [])
        upsert_documents = [
            ProductDocument(
                id=uuid.uuid4(),
                product_id=1,
                umid=uuid.uuid4(),
                article_number="A1",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            )
        ]

        results = self.opensearch_client.delta_update(upsert_documents, None)

        self.assertEqual(results["upserted"], (1, []))
        self.assertEqual(results["deleted"], (0, []))
        self.assertEqual(mock_bulk.call_count, 1)  # Only one bulk call for upserts

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_delta_update_delete_only(self, mock_bulk):
        """Test delta update with only delete IDs."""
        mock_bulk.return_value = (1, [])
        delete_ids = ["id1", "id2"]

        results = self.opensearch_client.delta_update(None, delete_ids)

        self.assertEqual(results["upserted"], (0, []))
        self.assertEqual(results["deleted"], (1, []))
        self.assertEqual(mock_bulk.call_count, 1)  # Only one bulk call for deletes

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_delta_update_both_operations(self, mock_bulk):
        """Test delta update with both upsert and delete operations."""
        mock_bulk.return_value = (1, [])
        upsert_documents = [
            ProductDocument(
                id=uuid.uuid4(),
                product_id=1,
                umid=uuid.uuid4(),
                article_number="A1",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            )
        ]
        delete_ids = ["id1", "id2"]

        results = self.opensearch_client.delta_update(upsert_documents, delete_ids)

        self.assertEqual(results["upserted"], (1, []))
        self.assertEqual(results["deleted"], (1, []))
        self.assertEqual(mock_bulk.call_count, 2)  # Two bulk calls - one for upserts, one for deletes

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_delta_update_no_operations(self, mock_bulk):
        """Test delta update with no operations to perform."""
        results = self.opensearch_client.delta_update(None, None)

        self.assertEqual(results["upserted"], (0, []))
        self.assertEqual(results["deleted"], (0, []))
        mock_bulk.assert_not_called()  # No bulk operations should be called

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.bulk")
    def test_delta_update_exception(self, mock_bulk):
        """Test exception handling in delta update."""
        mock_bulk.side_effect = Exception("Delta update error")
        upsert_documents = [
            ProductDocument(
                id=uuid.uuid4(),
                product_id=1,
                umid=uuid.uuid4(),
                article_number="A1",
                product_names=[],
                master_product_names=[],
                last_modified=datetime.now(),
                category_names=[],
            )
        ]

        with self.assertRaises(Exception):
            self.opensearch_client.delta_update(upsert_documents, None)

    def test_reindex_success(self):
        """Test successful reindex operation."""
        with (
            patch.object(self.opensearch_client, "delete_index") as mock_delete,
            patch.object(self.opensearch_client, "create_index") as mock_create,
            patch.object(self.opensearch_client, "bulk_index") as mock_bulk,
        ):
            mock_bulk.return_value = (2, [])
            mappings = {"properties": {"field": {"type": "keyword"}}}
            documents = [
                ProductDocument(
                    id=uuid.uuid4(),
                    product_id=1,
                    umid=uuid.uuid4(),
                    article_number="A1",
                    product_names=[],
                    master_product_names=[],
                    last_modified=datetime.now(),
                    category_names=[],
                )
            ]

            success, failed = self.opensearch_client.reindex(mappings, documents)

            mock_delete.assert_called_once_with()
            mock_create.assert_called_once_with(mappings)
            mock_bulk.assert_called_once_with(documents)
            self.assertEqual(success, 2)
            self.assertEqual(failed, [])

    def test_reindex_exception(self):
        """Test exception handling in reindex operation."""
        with patch.object(self.opensearch_client, "delete_index") as mock_delete:
            mock_delete.side_effect = Exception("Reindex error")
            mappings = {"properties": {"field": {"type": "keyword"}}}
            documents = []

            with self.assertRaises(Exception):
                self.opensearch_client.reindex(mappings, documents)

    @patch("holmatro_customer_portal.utils.open_search.opensearch_client.OpenSearchQueryBuilder")
    def test_filter_products_includes_aggregations(self, mock_open_search_query_builder):
        mock_query_builder_instance = mock_open_search_query_builder.return_value
        mock_query_builder_instance.build.return_value = {
            "query": {"match_all": {}},
            "from": 0,
            "size": 100,  # Default page_size for filter_products
            "sort": [{"_score": {"order": "desc"}}],
            "aggs": {"filterable_attributes": {}},  # Expected aggregation structure
        }
        self.mock_client.search.return_value = {"hits": {"hits": [], "total": {"value": 0}}}

        self.opensearch_client.filter_products()

        mock_open_search_query_builder.assert_called_once_with(self.opensearch_client.is_fuzzy_search_enabled)
        mock_query_builder_instance.build.assert_called_once_with(
            category_id=None,
            filters=None,
            page_number=1,
            page_size=100,
            build_aggregations=True,
        )

        # Assert OpenSearch client's search method was called with aggregations
        self.mock_client.search.assert_called_once()
        called_body = self.mock_client.search.call_args[1]["body"]
        self.assertIn("aggs", called_body)
        self.assertEqual(called_body["aggs"], {"filterable_attributes": {}})
