from __future__ import annotations

import math
from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ProductPreviewRes
from holmatro_customer_portal.services.configurator.configurator_enums import (
    CylinderType,
    FilterName,
    SyncForceAttributeId,
)
from holmatro_customer_portal.services.configurator.default_products import CylinderEyes, CylinderProtectionSpring
from holmatro_customer_portal.services.configurator.queries import (
    get_configuration_data_value,
    get_configuration_product_attribute,
)
from holmatro_customer_portal.services.configurator.states.base import (
    BaseState,
    ConfiguratorCylinderStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.utils import get_or_query_cylinder_quantity
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_article_number
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class CylinderSettings(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_SETTINGS


class CylinderProductList(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST

    def on_enter(self: BaseState | Configurator, event: EventData) -> None:
        """Add and prefill the cylinder product list filters."""
        main = get_configuration_data_value(
            self._session, self._configuration, ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_MAIN.value
        )

        filters = None

        def get_category_path(product_category_id: SyncforceCategory):
            return self._catalog_repository.get_category_path(
                self._configuration.user.language.code, product_category_id, SyncforceCategory.INDUSTRIAL_LIFTING
            )

        category_path = get_category_path(SyncforceCategory.CYLINDERS)

        def get_lifting_cylinder_type_filter() -> str | None:
            """Fetches all the cylinder types and filter out the options 'pulling' and 'hollow plunger'"""
            cylinder_type_filters = self._catalog_repository.get_facet_attributes(
                FilterName.CYLINDER_TYPE.value, SyncforceCategory.CYLINDERS, SyncforceCategory.INDUSTRIAL_LIFTING
            )
            if not cylinder_type_filters:
                _logger.error("Tweakwise cylinder filters fails")
                return None
            return "|".join(
                filter(
                    lambda c_filter: c_filter not in ["pulling", "hollow plunger"],
                    ([c_attribute.title for c_attribute in cylinder_type_filters]),
                )
            )

        match main:
            case "application":
                cylinder_type = get_configuration_data_value(
                    self._session,
                    self._configuration,
                    ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_APPLICATION.value,
                )

                match cylinder_type:
                    case "lifting":
                        lifting_type = get_configuration_data_value(
                            self._session,
                            self._configuration,
                            ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_APPLICATION_LIFTING.value,
                        )

                        _logger.debug(f"Matching cylinder lifting type: {lifting_type}")
                        match lifting_type:
                            case CylinderType.LIFTING_CYLINDERS.value:
                                if tweakwise_filters := get_lifting_cylinder_type_filter():
                                    filters = {FilterName.CYLINDER_TYPE_MULTILANGUAGE.value: tweakwise_filters}

                            case CylinderType.LIFTING_WEDGES.value:
                                category_path = get_category_path(SyncforceCategory.CYLINDER_WEDGES)

                            case CylinderType.LIFTING_TOE_JACKS.value:
                                category_path = get_category_path(SyncforceCategory.CYLINDER_TOE_JACKS)

                            case _:
                                if tweakwise_filters := get_lifting_cylinder_type_filter():
                                    filters = {FilterName.CYLINDER_TYPE_MULTILANGUAGE.value: tweakwise_filters}
                                category_path = None

                    case "pulling":
                        """
                        If cylinder is pulling, the cylinder types will be 'pulling' or 'hollow cylinder'
                        which are also filters
                        """
                        pulling_type = get_configuration_data_value(
                            self._session,
                            self._configuration,
                            ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_APPLICATION_PULLING.value,
                        )
                        if pulling_type:
                            filters = {FilterName.CYLINDER_TYPE_MULTILANGUAGE.value: pulling_type}

                    case _:
                        pass

            case "calculation":
                weight = get_configuration_data_value(
                    self._session,
                    self._configuration,
                    ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_CALCULATION_WEIGHT.value,
                )
                symmetry = get_configuration_data_value(
                    self._session,
                    self._configuration,
                    ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_CALCULATION_SYMMETRY.value,
                )
                lifting_points = get_configuration_data_value(
                    self._session,
                    self._configuration,
                    ConfiguratorCylinderStageDataIDs.CYLINDER_SETTINGS_CALCULATION_LIFTING_POINTS.value,
                )
                if lifting_points:
                    for c in self.state_definition["content"]:
                        if c["type"] == "products":
                            c["meta"]["initial"] = lifting_points

                if all((weight, symmetry, lifting_points)):
                    try:
                        factor = 1.5 if symmetry == "symmetrical" else 2.0
                        tonnage = math.ceil(int(weight) * factor / int(lifting_points))
                        facet_attributes = self._catalog_repository.get_facet_attributes(
                            FilterName.TONNAGE.value, SyncforceCategory.CYLINDERS, SyncforceCategory.INDUSTRIAL_LIFTING
                        )
                        if facet_attributes:
                            max_tonnage = max((int(a.title) for a in facet_attributes))
                        else:
                            max_tonnage = 300
                        filters = {FilterName.TONNAGE.value: f"{tonnage}-{max_tonnage}"}
                        if tweakwise_filters := get_lifting_cylinder_type_filter():
                            filters.update({FilterName.CYLINDER_TYPE_MULTILANGUAGE.value: tweakwise_filters})
                    except ValueError:
                        # It will return the Cylinders category already defined
                        pass

                else:
                    # It will return the Cylinders category already defined
                    pass

            case _:
                # It will return the Cylinders category already defined
                pass

        for c in self.state_definition["content"]:
            if c["type"] == "filters":
                c["meta"]["filters"] = filters
                c["meta"]["category"] = category_path


class CylinderPullingClevisEyes(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_CLEVIS_EYES

    def on_enter(self: BaseState | Configurator, event: EventData):
        number_of_cylinders = get_or_query_cylinder_quantity(event, self._session, self._configuration)

        def get_products(product_article_number: list[str] | str) -> list[ProductPreviewRes]:
            products = get_product_preview_by_article_number(
                product_article_number, self._configuration.user, self._session
            )
            return ProductPreviewMapper(self._session, self._my_hm_client).product_preview_mapper(
                self._configuration.user, products
            )

        cylinder_tonnage = get_configuration_product_attribute(
            self._session,
            self._configuration,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
            SyncForceAttributeId.CYLINDER_TONNAGE,
        )

        match cylinder_tonnage:
            case "11":
                products = get_products(
                    [CylinderEyes.PULLING_EYES_CYLINDER_11T_30T.value, CylinderEyes.CLEVIS_EYES_CYLINDER_11T.value]
                )
            case "30":
                products = get_products(
                    [CylinderEyes.PULLING_EYES_CYLINDER_11T_30T.value, CylinderEyes.CLEVIS_EYES_CYLINDER_30T.value]
                )
            case "60":
                products = get_products(CylinderEyes.PULLING_EYES_CYLINDER_60T.value)
            case _:
                products = [product.value for product in CylinderEyes]

        for c in self.state_definition["content"]:
            if c["type"] == "products":
                c["meta"]["initial"] = number_of_cylinders * 2
                c["meta"]["products"] = products


class CylinderPullingProtectionSpring(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PULLING_PROTECTION_SPRING

    def on_enter(self: BaseState | Configurator, event: EventData):
        number_of_cylinders = get_or_query_cylinder_quantity(event, self._session, self._configuration)

        def get_products(product_article_number: list[str] | str) -> list[ProductPreviewRes]:
            products = get_product_preview_by_article_number(
                product_article_number, self._configuration.user, self._session
            )
            return ProductPreviewMapper(self._session, self._my_hm_client).product_preview_mapper(
                self._configuration.user, products
            )

        cylinder_tonnage = get_configuration_product_attribute(
            self._session,
            self._configuration,
            ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
            SyncForceAttributeId.CYLINDER_TONNAGE,
        )

        match cylinder_tonnage:
            case "11" | "30":
                products = get_products(CylinderProtectionSpring.PROTECTION_SPRING_11T_30T.value)
            case "60":
                products = get_products(CylinderProtectionSpring.PROTECTION_SPRING_60T.value)
            case _:
                products = [product.value for product in CylinderProtectionSpring]

        for c in self.state_definition["content"]:
            if c["type"] == "products":
                c["meta"]["initial"] = number_of_cylinders
                c["meta"]["products"] = products
