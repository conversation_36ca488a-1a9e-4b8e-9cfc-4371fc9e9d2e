from __future__ import annotations

import math
from typing import TYPE_CHECKING

from transitions import EventData

from holmatro_customer_portal.database.models import Configuration
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import (
    ParamsReq,
    ProductPreviewRes,
    SearchFiltersReq,
    SearchParamsReq,
)
from holmatro_customer_portal.services.configurator.configurator_enums import (
    CylinderActingType,
    FilterName,
    PumpType,
    SyncForceAttributeId,
)
from holmatro_customer_portal.services.configurator.default_products import Pumps
from holmatro_customer_portal.services.configurator.queries import (
    get_configuration_product_attribute,
    get_configuration_products,
)
from holmatro_customer_portal.services.configurator.states.base import (
    BaseState,
    ConfiguratorPumpStageDataIDs,
    ConfiguratorState,
)
from holmatro_customer_portal.services.configurator.utils import (
    get_or_query_cylinder_acting_type,
    get_or_query_cylinder_quantity,
    get_or_query_pump_type,
    get_or_query_value,
)
from holmatro_customer_portal.services.utils.product_queries import get_product_preview_by_article_number
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import SyncforceCategory
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.tweakwise.tweakwise_client import TweakwiseClient

if TYPE_CHECKING:
    from holmatro_customer_portal.services.configurator.workflow import Configurator

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class PumpSettingsActing(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS_ACTING

    def on_enter(self: BaseState | Configurator, event: EventData) -> bool:
        """Remove settings according to the user choice's for cylinders"""
        cylinder = get_configuration_products(
            self._session, self._configuration, ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST
        )
        if cylinder:
            self.state_definition["content"] = [
                c
                for c in self.state_definition["content"]
                if c["id"] == ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_NUMBER_OF_PUMPS.value
            ]
            quantity = get_or_query_cylinder_quantity(event, self._session, self._configuration)
            for c in self.state_definition["content"]:
                if c["id"] == ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_NUMBER_OF_PUMPS.value:
                    c["meta"]["options"] = list(range(1, quantity + 1))

        else:
            self.state_definition["content"] = [
                c
                for c in self.state_definition["content"]
                if c["id"] == ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_ACTING_PLURALITY.value
            ]


class PumpSettings(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_SETTINGS

    def on_enter(self: BaseState | Configurator, event: EventData) -> bool:
        """
        1. Check cylinder oil content to filter pump types which don't support the cylinder specifications
        2. Remove settings which only apply to single acting systems
        """
        _logger.debug(f"Fetching on enter information for {self.state}")

        required_oil_content = _get_required_oil_content(self._session, self._configuration, event)

        mapped_pumps = {
            PumpType.COMPACT.value: SyncforceCategory.COMPACT_PUMPS,
            PumpType.VARI.value: SyncforceCategory.VARI_PUMPS,
            PumpType.HAND.value: SyncforceCategory.HAND_AND_FOOT_PUMPS,
        }
        pump_types = [PumpType.EXISTING.value]
        for pump_type, pump_path in mapped_pumps.items():
            category_path = self._catalog_repository.get_category_path(
                "en", pump_path, SyncforceCategory.INDUSTRIAL_LIFTING
            )

            params = ParamsReq(category_path=category_path, page_number=1, products_per_page=100)
            filters = SearchFiltersReq(
                filters={FilterName.CAPACITY_OIL_TANK.value: f"{required_oil_content or 1}-50000"}
            )
            products = self._catalog_repository.get_products(params, filters)

            if products.items:
                pump_types.append(pump_type)

        for c in self.state_definition["content"]:
            if c["id"] == ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE.value:
                c["meta"]["choices"] = [o for o in c["meta"]["choices"] if o["value"] in pump_types]

        cylinder_acting_type = get_or_query_cylinder_acting_type(event, self._session, self._configuration)
        if cylinder_acting_type == CylinderActingType.DOUBLE.value:
            for c in self.state_definition["content"]:
                if c["id"] == ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE.value:
                    c["meta"]["choices"] = [
                        o
                        for o in c["meta"]["choices"]
                        if o["title"] != ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_TYPE_COMPACT.value
                    ]

        elif cylinder_acting_type == CylinderActingType.SINGLE.value:
            self.state_definition["content"] = [
                c
                for c in self.state_definition["content"]
                if c["id"] != ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_HAND_VALVE.value
            ]


class PumpProductList(BaseState):
    state = ConfiguratorState.INDUSTRIAL_LIFTING_PUMP_PRODUCTLIST

    def on_enter(self: BaseState | Configurator, event: EventData) -> bool:
        """
        1: Determine oil content based on cylinder (or none if not selected)
        2: Determine pump type (hand/compact/vari/none)
        3: If pump vari, check cylinder acting type. If single, filter out pumps with second stage output > 1500
        """
        _logger.debug(f"Fetching on enter information for {self.state}")

        filters = {}

        cylinder_quantity: int | None = get_or_query_cylinder_quantity(event, self._session, self._configuration)
        number_of_pumps: int = get_or_query_value(
            event, ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_NUMBER_OF_PUMPS.value, self._session, self._configuration
        )

        required_oil_content = _get_required_oil_content(self._session, self._configuration, event)
        if required_oil_content:
            facet_attributes = self._catalog_repository.get_facet_attributes(
                FilterName.CAPACITY_OIL_TANK.value,
                SyncforceCategory.HYDRAULIC_PUMPS,
                SyncforceCategory.INDUSTRIAL_LIFTING,
            )
            if facet_attributes:
                max_oil_content = max((int(a.title) for a in facet_attributes))
            else:
                max_oil_content = 50000

            filters.update({FilterName.CAPACITY_OIL_TANK.value: f"{required_oil_content}-{max_oil_content}"})

        products = None
        category_path = None
        pump_type = get_or_query_pump_type(event, self._session, self._configuration)

        def get_products(product_article_number: list[str] | str) -> list[ProductPreviewRes]:
            products = get_product_preview_by_article_number(
                product_article_number, self._configuration.user, self._session
            )
            return ProductPreviewMapper(self._session, self._my_hm_client).product_preview_mapper(
                self._configuration.user, products
            )

        def get_category_path(product_category_id: SyncforceCategory):
            return self._catalog_repository.get_category_path(
                self._configuration.user.language.code, product_category_id, SyncforceCategory.INDUSTRIAL_LIFTING
            )

        _logger.debug(f"Matching pump type: {pump_type}")
        match pump_type:
            case PumpType.HAND.value:
                category_path = get_category_path(SyncforceCategory.HAND_AND_FOOT_PUMPS)
                filters.update({FilterName.PUMP_OPERATOR_TYPE.value: PumpType.HAND.value})

            case PumpType.COMPACT.value:
                powersource = get_or_query_value(
                    event,
                    ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_COMPACT_POWER.value,
                    self._session,
                    self._configuration,
                )

                _logger.debug(f"Matching compact pump power source: {powersource}")
                match powersource:
                    case PumpType.COMPACT_ELECTRIC.value:
                        _logger.debug(f"Matching compact pump electric")
                        products = get_products(Pumps.COMPACT_PUMP_ELECTRIC.value)

                    case PumpType.COMPACT_AIR.value:
                        _logger.debug(f"Matching compact pump air")
                        category_path = get_category_path(SyncforceCategory.COMPACT_PUMPS)
                        filters.update({FilterName.PUMP_OPERATOR_TYPE.value: PumpType.COMPACT_AIR.value})

                    case _:
                        category_path = get_category_path(SyncforceCategory.COMPACT_PUMPS)

            case PumpType.VARI.value:
                category_path = get_category_path(SyncforceCategory.VARI_PUMPS)
                # Number of outputs filters out other products in the category that are not pumps
                filters.update({FilterName.NUMBER_OF_OUTPUTS.value: 1})  # Set to 1 by Holmatro choice

                cylinder_acting_type = get_or_query_cylinder_acting_type(event, self._session, self._configuration)
                if cylinder_acting_type == CylinderActingType.SINGLE.value:
                    # These filters are by Holmatro choice to exclude 18W type pumps
                    filters.update({FilterName.FIRST_STAGE_OUTPUT_CC.value: "0-3200"})
                    filters.update({FilterName.FIRST_STAGE_OUTPUT_OZ.value: "0-105"})

            case _:
                category_path = get_category_path(SyncforceCategory.HYDRAULIC_PUMPS)

        for c in self.state_definition["content"]:
            if c["type"] == "products":
                if number_of_pumps:
                    c["meta"]["initial"] = number_of_pumps

                if cylinder_quantity:
                    c["meta"]["maximum"] = cylinder_quantity

        if products:
            for c in self.state_definition["content"]:
                if c["type"] == "products":
                    c["meta"]["products"] = products
                return

        if filters:
            for c in self.state_definition["content"]:
                if c["type"] == "filters":
                    c["meta"]["filters"] = filters

        if category_path:
            for c in self.state_definition["content"]:
                if c["type"] == "filters":
                    c["meta"]["category"] = category_path


def _get_required_oil_content(db: Session, configuration: Configuration, event: EventData) -> int | None:
    cylinder_content = get_configuration_product_attribute(
        db,
        configuration,
        ConfiguratorState.INDUSTRIAL_LIFTING_CYLINDER_PRODUCTLIST,
        SyncForceAttributeId.CYLINDER_REQUIRED_OIL_CONTENT,
    )

    cylinder_quantity = get_or_query_cylinder_quantity(event, db, configuration)
    number_of_pumps = get_or_query_value(
        event, ConfiguratorPumpStageDataIDs.PUMP_SETTINGS_NUMBER_OF_PUMPS.value, db, configuration
    )
    if cylinder_content and (cylinder_quantity or number_of_pumps):
        required_oil_content = math.ceil(
            float(cylinder_content) * cylinder_quantity * 1.2 / (number_of_pumps or cylinder_quantity)
        )
        return required_oil_content

    return None
