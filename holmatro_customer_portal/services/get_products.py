from holmatro_customer_portal.database.models import User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.mappers.product_preview_mapper import ProductPreviewMapper
from holmatro_customer_portal.schemas.response_schema import ParamsReq, ProductsWithFiltersRes, SearchFiltersReq
from holmatro_customer_portal.services.products.parse_products_and_properties import (
    get_db_products,
    parse_to_paginated_results,
)
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.my_hm.my_hm_interface import MyHolmatroClientInterface


def get_products_with_filters_handle(
    catalog_repository: CatalogRepository,
    params: ParamsReq,
    filters: SearchFiltersReq | None,
    user: User,
    db: Session,
    my_hm_client: MyHolmatroClientInterface,
) -> ProductsWithFiltersRes:

    navigation_result = catalog_repository.get_navigation(user, params, filters)

    if not navigation_result.items:
        products, parsed_properties = parse_to_paginated_results([], params)

    else:
        base_category_id = params.category_id
        sub_category_id = None

        if params.category_path:
            category_path_chunks = params.category_path.split("-")
            sub_category_id = int(category_path_chunks[-1][4:])
        db_products = get_db_products(navigation_result, user, db, base_category_id, sub_category_id)
        parsed_products, parsed_properties = parse_to_paginated_results(db_products, params)

        mapper = ProductPreviewMapper(db, my_hm_client)
        products = mapper.product_preview_mapper(user, parsed_products)

    return ProductsWithFiltersRes(products=products, properties=parsed_properties, filters=navigation_result.facets)
