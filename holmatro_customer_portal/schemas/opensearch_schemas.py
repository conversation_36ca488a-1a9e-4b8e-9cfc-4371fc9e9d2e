from datetime import datetime
from typing import Any, Dict, List
from uuid import UUID

from pydantic import BaseModel


class FilterConfig(BaseModel):
    attribute_id: str
    is_numeric_value: bool = False
    values: List[Any] | None = None
    min_value: float | None = None
    max_value: float | None = None


class TranslatedName(BaseModel):
    language_code: str
    value: str


class FilterableAttribute(BaseModel):
    attribute_id: int
    attribute_name: str
    value_string: str | None = None
    value_numeric: float | None = None


class ProductDocument(BaseModel):
    id: UUID
    product_id: int | None
    umid: UUID | None
    article_number: str | None
    synonyms: str | None = None
    product_names: List[TranslatedName]
    master_product_names: List[TranslatedName]
    category_names: List[TranslatedName]
    category_ids: List[str] | None = None
    filterable_attributes: List[FilterableAttribute] = []
    last_modified: datetime | None = None
    # Completion suggester fields
    product_name_suggest: List[str] | None = None
    article_number_suggest: List[str] | None = None

    class Config:
        json_encoders = {
            UUID: str,
            datetime: lambda dt: dt.isoformat() if dt else None,
        }
        from_attributes = True


class AutocompleteSuggestion(BaseModel):
    text: str
    score: float


class AutocompleteProduct(BaseModel):
    id: UUID
    product_id: int | None
    product_names: List[TranslatedName]
    score: float


class AutocompleteResponse(BaseModel):
    suggestions: List[str]
    products: List[AutocompleteProduct]


class OpenSearchSearchResponse(BaseModel):
    hits: Dict[str, Any]
    aggregations: Dict[str, Any] | None = None
