from fastapi import APIRouter, Depends, status

from holmatro_customer_portal.schemas.request_schema import ResyncProductsRequest
from holmatro_customer_portal.services.import_product import (
    import_products_from_storage,
    schedule_full_resync_with_cleanup, soft_delete_categories_not_in_proposition,
)
from holmatro_customer_portal.utils.auth.auth import get_jwt_token
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.storage_utils import get_api_json_contents

router = APIRouter()
_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


@router.post(
    "/resync_products",
    status_code=status.HTTP_200_OK,
    summary="Import Syncforce products from BLOB",
    description=(
        "Triggers the import of product detail files from BLOB. "
        "The import process runs on the background. "
        "Optionally accepts a list of specific product_ids to sync instead of all products."
    ),
)
def resync_products(
    request: ResyncProductsRequest = ResyncProductsRequest(), jwt_token: str = Depends(get_jwt_token)
) -> dict:
    process_assets = request.process_assets
    if request.product_ids:
        # Use the provided product IDs - only import, no soft-deletion
        product_ids = request.product_ids
        import_products_from_storage.delay(product_ids, process_assets)
        message = f"Products import started for {len(product_ids)} specific products (no cleanup)."
    else:
        # Get all products from the proposition with cleanup
        proposition_products = get_api_json_contents(f"categories-{Env.SYNC_FORCE_PROPOSITION_ID.get()}.json")
        product_ids = list(proposition_products.keys())  # type: ignore

        # Safeguard: Check if we have a reasonable number of products
        min_products_threshold = Env.MIN_PRODUCTS_PROPOSITION_THRESHOLD.get()
        if len(product_ids) < min_products_threshold:
            error_msg = (
                f"Refusing to start full resync: proposition contains only {len(product_ids)} products, "
                f"which is below the minimum threshold of {min_products_threshold}. "
                f"This could indicate a data issue with the proposition file."
            )
            return {"status": "error", "message": error_msg}

        schedule_full_resync_with_cleanup.delay(product_ids, process_assets)
        message = f"Products import started for all {len(product_ids)} products in the proposition (with cleanup)."

    return {"status": "success", "message": message}



@router.post(
    "/cleanup_categories",
    status_code=status.HTTP_200_OK,
    summary="Cleanup categories not in proposition",
    description=(
        "Soft-deletes categories that are not present in the current proposition file. "
        "This should be called after a full product sync to clean up orphaned categories."
    ),
)
def cleanup_categories(jwt_token: str = Depends(get_jwt_token)) -> dict:
    """
    Endpoint to trigger category cleanup based on the current proposition file.
    """
    try:
        active_category_umids = soft_delete_categories_not_in_proposition()

        message = f"Category cleanup completed. {len(active_category_umids)} categories remain active."
        return {"status": "success", "message": message}

    except Exception as e:
        error_message = f"Category cleanup failed: {str(e)}"
        _logger.error(error_message)
        return {"status": "error", "message": error_message}
